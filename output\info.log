2025-08-05 09:33:22 - INFO - Log initialization complete
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: language
2025-08-05 09:33:22 - INFO - 多厂商配置转换工具已启动，模式: convert, 厂商: fortigate, 语言: [缺失:language]
2025-08-05 09:33:22 - INFO - 开始转换配置文件
2025-08-05 09:33:22 - INFO - 目标型号: z3200s
2025-08-05 09:33:22 - INFO - 目标版本: R11
2025-08-05 09:33:22 - INFO - PhysicalInterfaceHandler初始化 - operation_mode_result为空
2025-08-05 09:33:22 - INFO - PhysicalInterfaceHandler初始化 - operation_mode_result为空
2025-08-05 09:33:22 - INFO - PhysicalInterfaceHandler初始化 - operation_mode_result为空
2025-08-05 09:33:22 - INFO - NTOS内置服务已加载: 82 个
2025-08-05 09:33:22 - INFO - 厂商映射已加载: fortigate，数量: 208
2025-08-05 09:33:22 - INFO - 服务映射已加载: 104 个
2025-08-05 09:33:22 - INFO - 服务别名已加载: 69 个
2025-08-05 09:33:22 - INFO - name_mapping_manager.initialized
2025-08-05 09:33:22 - INFO - name_mapping_manager.initialized
2025-08-05 09:33:22 - INFO - 转换前验证配置
2025-08-05 09:33:22 - INFO - 开始验证配置文件
2025-08-05 09:33:22 - INFO - 最低版本要求
2025-08-05 09:33:22 - INFO - 检测到FortiOS版本: 7.0.17
2025-08-05 09:33:22 - INFO - 配置文件验证通过
2025-08-05 09:33:22 - INFO - 验证通过
2025-08-05 09:33:22 - INFO - 使用新架构进行转换
2025-08-05 09:33:22 - INFO - 配置管理器：检测到开发环境
2025-08-05 09:33:22 - INFO - 配置管理器已初始化
2025-08-05 09:33:22 - INFO - 缓存管理器已初始化
2025-08-05 09:33:22 - INFO - 性能监控器已初始化
2025-08-05 09:33:22 - INFO - 缓存管理器：缓存已创建
2025-08-05 09:33:22 - INFO - 模板管理器已初始化
2025-08-05 09:33:22 - INFO - YANG管理器已初始化
2025-08-05 09:33:22 - INFO - 性能监控器已初始化
2025-08-05 09:33:22 - INFO - 内存管理器已初始化
2025-08-05 09:33:22 - INFO - 异常注册表已初始化
2025-08-05 09:33:22 - INFO - 错误处理器已初始化
2025-08-05 09:33:22 - INFO - 转换工作流程已初始化
2025-08-05 09:33:22 - INFO - 转换服务已初始化
2025-08-05 09:33:22 - INFO - 开始转换
2025-08-05 09:33:22 - INFO - 模板已找到
2025-08-05 09:33:22 - INFO - YANG模型已找到
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: urn
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: urn
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: urn:ruijie:ntos
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: urn
2025-08-05 09:33:22 - WARNING - 命名空间不匹配: 实际='None', 期望='urn:ruijie:ntos'
2025-08-05 09:33:22 - INFO - 模板文件命名空间保持原样，不进行修改
2025-08-05 09:33:22 - INFO - 模板加载成功: 型号=z3200s, 版本=R11, 类型=running
2025-08-05 09:33:22 - INFO - 找到YANG文件
2025-08-05 09:33:22 - INFO - 模式已加载
2025-08-05 09:33:22 - INFO - YANG管理器：模式已加载
2025-08-05 09:33:22 - INFO - YANG管理器：命名空间已加载
2025-08-05 09:33:22 - INFO - name_mapping_manager.initialized
2025-08-05 09:33:22 - INFO - 管道管理器：已初始化
2025-08-05 09:33:22 - INFO - Interface mapping loaded
2025-08-05 09:33:22 - INFO - Interface mapping loaded (flat format): 7 mappings
2025-08-05 09:33:22 - INFO - 转换策略已初始化
2025-08-05 09:33:22 - INFO - Fortigate策略阶段已初始化
2025-08-05 09:33:22 - INFO - XML模板集成阶段已初始化
2025-08-05 09:33:22 - INFO - 开始解析配置文件
2025-08-05 09:33:22 - INFO - 成功读取文件，共 610 行
2025-08-05 09:33:22 - INFO - 配置文件读取成功，共 610 行
2025-08-05 09:33:22 - INFO - fortigate.detected_transparent_mode_from_header
2025-08-05 09:33:22 - INFO - 开始解析系统全局配置部分
2025-08-05 09:33:22 - INFO - fortigate.set_admin_sport
2025-08-05 09:33:22 - INFO - fortigate.set_admin_ssh_port
2025-08-05 09:33:22 - INFO - fortigate.set_hostname
2025-08-05 09:33:22 - INFO - fortigate.set_timezone
2025-08-05 09:33:22 - INFO - fortigate.system_settings_section_end
2025-08-05 09:33:22 - INFO - 未知 配置部分结束
2025-08-05 09:33:22 - INFO - 未知 配置部分结束
2025-08-05 09:33:22 - INFO - 未知 配置部分结束
2025-08-05 09:33:22 - INFO - 开始解析接口配置部分
2025-08-05 09:33:22 - INFO - 解析接口: ha
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 10
2025-08-05 09:33:22 - INFO - 完成接口 ha 解析
2025-08-05 09:33:22 - INFO - 解析接口: mgmt
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ***********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 9
2025-08-05 09:33:22 - INFO - 完成接口 mgmt 解析
2025-08-05 09:33:22 - INFO - 解析接口: port1
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 2
2025-08-05 09:33:22 - INFO - 完成接口 port1 解析
2025-08-05 09:33:22 - INFO - 解析接口: port2
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ***********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 12
2025-08-05 09:33:22 - INFO - 完成接口 port2 解析
2025-08-05 09:33:22 - INFO - 解析接口: port3
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): **********/16
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 19
2025-08-05 09:33:22 - INFO - 完成接口 port3 解析
2025-08-05 09:33:22 - INFO - 解析接口: port4
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 14
2025-08-05 09:33:22 - INFO - 完成接口 port4 解析
2025-08-05 09:33:22 - INFO - 解析接口: port5
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *************/30
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 15
2025-08-05 09:33:22 - INFO - 完成接口 port5 解析
2025-08-05 09:33:22 - INFO - 解析接口: port6
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ***********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 8
2025-08-05 09:33:22 - INFO - 完成接口 port6 解析
2025-08-05 09:33:22 - INFO - 解析接口: port7
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 7
2025-08-05 09:33:22 - INFO - 完成接口 port7 解析
2025-08-05 09:33:22 - INFO - 解析接口: port8
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 6
2025-08-05 09:33:22 - INFO - 完成接口 port8 解析
2025-08-05 09:33:22 - INFO - 解析接口: port9
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ***********/26
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口状态: down
2025-08-05 09:33:22 - INFO - 设置接口启用状态: False
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置接口角色: wan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 5
2025-08-05 09:33:22 - INFO - 完成接口 port9 解析
2025-08-05 09:33:22 - INFO - 解析接口: port10
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 3
2025-08-05 09:33:22 - INFO - 完成接口 port10 解析
2025-08-05 09:33:22 - INFO - 解析接口: port11
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 4
2025-08-05 09:33:22 - INFO - 完成接口 port11 解析
2025-08-05 09:33:22 - INFO - 解析接口: port12
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 20
2025-08-05 09:33:22 - INFO - 完成接口 port12 解析
2025-08-05 09:33:22 - INFO - 解析接口: s1
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 1
2025-08-05 09:33:22 - INFO - 完成接口 s1 解析
2025-08-05 09:33:22 - INFO - 解析接口: s2
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 16
2025-08-05 09:33:22 - INFO - 完成接口 s2 解析
2025-08-05 09:33:22 - INFO - 解析接口: vw1
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 17
2025-08-05 09:33:22 - INFO - 完成接口 vw1 解析
2025-08-05 09:33:22 - INFO - 解析接口: vw2
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 18
2025-08-05 09:33:22 - INFO - 完成接口 vw2 解析
2025-08-05 09:33:22 - INFO - 解析接口: x1
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *************/26
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置接口角色: wan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 13
2025-08-05 09:33:22 - WARNING - 无效的MTU格式: set mtu-override enable
2025-08-05 09:33:22 - INFO - 完成接口 x1 解析
2025-08-05 09:33:22 - INFO - 解析接口: x2
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ***********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping', 'https', 'ssh', 'snmp']
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 11
2025-08-05 09:33:22 - INFO - 完成接口 x2 解析
2025-08-05 09:33:22 - INFO - 解析接口: modem
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口模式: pppoe
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['fabric']
2025-08-05 09:33:22 - INFO - 设置接口状态: down
2025-08-05 09:33:22 - INFO - 设置接口启用状态: False
2025-08-05 09:33:22 - INFO - 设置接口类型: physical
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 37
2025-08-05 09:33:22 - INFO - 设置默认网关: disable
2025-08-05 09:33:22 - WARNING - 警告: 接口 'modem' 设置了不支持的defaultgw disable选项
2025-08-05 09:33:22 - INFO - 完成接口 modem 解析
2025-08-05 09:33:22 - INFO - 解析接口: naf.root
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: tunnel
2025-08-05 09:33:22 - INFO - 信息: 接口 'naf.root' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 55
2025-08-05 09:33:22 - INFO - 完成接口 naf.root 解析
2025-08-05 09:33:22 - INFO - 解析接口: l2t.root
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: tunnel
2025-08-05 09:33:22 - INFO - 信息: 接口 'l2t.root' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 56
2025-08-05 09:33:22 - INFO - 完成接口 l2t.root 解析
2025-08-05 09:33:22 - INFO - 解析接口: ssl.root
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['fabric']
2025-08-05 09:33:22 - INFO - 设置接口类型: tunnel
2025-08-05 09:33:22 - INFO - 信息: 接口 'ssl.root' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 38
2025-08-05 09:33:22 - INFO - 完成接口 ssl.root 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vodafone_MPLS
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ***********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 21
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: port1
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 3452
2025-08-05 09:33:22 - INFO - 完成接口 Vodafone_MPLS 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan55
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): **********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 22
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 55
2025-08-05 09:33:22 - INFO - 完成接口 Vlan55 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan99
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 23
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 99
2025-08-05 09:33:22 - INFO - 完成接口 Vlan99 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan100
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): **********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 24
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 100
2025-08-05 09:33:22 - INFO - 完成接口 Vlan100 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan103
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 25
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 103
2025-08-05 09:33:22 - INFO - 完成接口 Vlan103 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan104
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 26
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 104
2025-08-05 09:33:22 - INFO - 完成接口 Vlan104 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan105
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 27
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 105
2025-08-05 09:33:22 - INFO - 完成接口 Vlan105 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan106
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 28
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 106
2025-08-05 09:33:22 - INFO - 完成接口 Vlan106 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan107
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 29
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 107
2025-08-05 09:33:22 - INFO - 完成接口 Vlan107 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan108
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 30
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 108
2025-08-05 09:33:22 - INFO - 完成接口 Vlan108 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan109
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 31
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 109
2025-08-05 09:33:22 - INFO - 完成接口 Vlan109 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan110
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 32
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 110
2025-08-05 09:33:22 - INFO - 完成接口 Vlan110 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan111
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 33
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 111
2025-08-05 09:33:22 - INFO - 完成接口 Vlan111 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan113
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 34
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 113
2025-08-05 09:33:22 - INFO - 完成接口 Vlan113 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan115
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 35
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 115
2025-08-05 09:33:22 - INFO - 完成接口 Vlan115 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan116
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 36
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 116
2025-08-05 09:33:22 - INFO - 完成接口 Vlan116 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan117
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/19
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping', 'snmp', 'radius-acct', 'fabric']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 39
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 117
2025-08-05 09:33:22 - INFO - 完成接口 Vlan117 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan120
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/23
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 40
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 120
2025-08-05 09:33:22 - INFO - 完成接口 Vlan120 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan136
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/22
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 41
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 136
2025-08-05 09:33:22 - INFO - 完成接口 Vlan136 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan150
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping', 'ssh', 'snmp', 'radius-acct']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 43
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 150
2025-08-05 09:33:22 - INFO - 完成接口 Vlan150 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan151
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 44
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 151
2025-08-05 09:33:22 - INFO - 完成接口 Vlan151 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan300
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/22
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 45
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 300
2025-08-05 09:33:22 - INFO - 完成接口 Vlan300 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan130
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): **********/16
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 47
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 130
2025-08-05 09:33:22 - INFO - 完成接口 Vlan130 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan155
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): **********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 48
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 155
2025-08-05 09:33:22 - INFO - 完成接口 Vlan155 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan200
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 49
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 200
2025-08-05 09:33:22 - INFO - 完成接口 Vlan200 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan222
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 50
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 222
2025-08-05 09:33:22 - INFO - 完成接口 Vlan222 解析
2025-08-05 09:33:22 - INFO - 解析接口: VLAN101
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): ********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping', 'https', 'ssh', 'snmp', 'http', 'radius-acct', 'fabric']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 52
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 101
2025-08-05 09:33:22 - INFO - 完成接口 VLAN101 解析
2025-08-05 09:33:22 - INFO - 解析接口: UITSEC_Forti
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: tunnel
2025-08-05 09:33:22 - INFO - 信息: 接口 'UITSEC_Forti' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 51
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x1
2025-08-05 09:33:22 - INFO - 完成接口 UITSEC_Forti 解析
2025-08-05 09:33:22 - INFO - 解析接口: UITSEC_CP
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: tunnel
2025-08-05 09:33:22 - INFO - 信息: 接口 'UITSEC_CP' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 53
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x1
2025-08-05 09:33:22 - INFO - 完成接口 UITSEC_CP 解析
2025-08-05 09:33:22 - INFO - 解析接口: BBS_PAM
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: tunnel
2025-08-05 09:33:22 - INFO - 信息: 接口 'BBS_PAM' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 54
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x1
2025-08-05 09:33:22 - INFO - 完成接口 BBS_PAM 解析
2025-08-05 09:33:22 - INFO - 解析接口: BBS_PAM1
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口类型: tunnel
2025-08-05 09:33:22 - INFO - 信息: 接口 'BBS_PAM1' 类型从 'tunnel' 转换为 'physical' 以兼容NTOS
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 57
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x1
2025-08-05 09:33:22 - INFO - 完成接口 BBS_PAM1 解析
2025-08-05 09:33:22 - INFO - 解析接口: TO-CHECKPOINT
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 58
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 145
2025-08-05 09:33:22 - INFO - 完成接口 TO-CHECKPOINT 解析
2025-08-05 09:33:22 - INFO - 解析接口: Vlan114
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *********/24
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 46
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 114
2025-08-05 09:33:22 - INFO - 完成接口 Vlan114 解析
2025-08-05 09:33:22 - INFO - 解析接口: RUIJIE-MGMT
2025-08-05 09:33:22 - INFO - 设置接口虚拟域: root
2025-08-05 09:33:22 - INFO - 设置接口IP (掩码): *************/23
2025-08-05 09:33:22 - INFO - 设置接口访问控制: ['ping', 'https']
2025-08-05 09:33:22 - INFO - 设置接口角色: lan
2025-08-05 09:33:22 - INFO - 设置SNMP索引: 42
2025-08-05 09:33:22 - INFO - 设置子接口所属的物理接口: x2
2025-08-05 09:33:22 - INFO - 设置子接口VLAN ID: 199
2025-08-05 09:33:22 - INFO - 完成接口 RUIJIE-MGMT 解析
2025-08-05 09:33:22 - INFO - 接口配置部分结束
2025-08-05 09:33:22 - INFO - 解析完成，找到 58 个接口, 0 个静态路由, 0 个区域, 0 个地址对象, 0 个地址组, 0 个服务对象, 0 个服务组
2025-08-05 09:33:22 - INFO - 解析完成，找到 58 个接口, 0 个静态路由, 0 个区域, 0 个地址对象, 0 个地址组, 0 个服务对象, 0 个服务组
2025-08-05 09:33:22 - INFO - Interface mapping loaded
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: 'mgmt': 'Ge0/0', 'port2': 'Ge0/2', 'port3': 'Ge0/3', 'port5': 'Ge0/5', 'port6': 'Ge0/6', 'port9': 'Ge0/1', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1'
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: 'mgmt'
2025-08-05 09:33:22 - INFO - 加载的接口映射: {'mgmt': 'Ge0/0', 'port2': 'Ge0/2', 'port3': 'Ge0/3', 'port5': 'Ge0/5', 'port6': 'Ge0/6', 'port9': 'Ge0/1', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1'}
2025-08-05 09:33:22 - INFO - 配置管理器：检测到开发环境
2025-08-05 09:33:22 - INFO - 配置管理器已初始化
2025-08-05 09:33:22 - INFO - YANG管理器已初始化
2025-08-05 09:33:22 - INFO - 验证服务已初始化
2025-08-05 09:33:22 - ERROR - 接口映射验证失败：发现 1 个无效映射
2025-08-05 09:33:22 - ERROR - 无效的接口映射：源接口 x2，目标接口 TenGe0/1，原因：接口 TenGe0/1 不在设备型号 z3200s 支持的接口列表中
2025-08-05 09:33:22 - ERROR - conversion_workflow.interface_mapping_validation_failed
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: initial_data.get('config_file', 'unknown')
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: initial_data
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: initial_data.get('vendor', 'unknown')
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: initial_data
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: initial_data.get('model', 'unknown')
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: initial_data
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: initial_data.get('version', 'unknown')
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: initial_data
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: '存在' if initial_data.get('interface_mapping') else '不存在'
2025-08-05 09:33:22 - WARNING - 消息中缺少参数: '存在' if initial_data
2025-08-05 09:33:22 - INFO - 阶段开始: fortigate_conversion, stage count: 12
2025-08-05 09:33:22 - INFO - 阶段开始: fortigate_conversion -> operation_mode (1/12)
2025-08-05 09:33:22 - INFO - 管道阶段：阶段开始
2025-08-05 09:33:22 - INFO - 开始检测操作模式
2025-08-05 09:33:22 - INFO - Operation mode analysis: explicit=nat, role_interfaces=True, mgmt_interface=True, manageip=False
2025-08-05 09:33:22 - INFO - Route mode detected from explicit configuration: nat
2025-08-05 09:33:22 - INFO - 处理路由模式配置
2025-08-05 09:33:22 - INFO - 路由模式处理完成，主机名: KHU-FGT-1
2025-08-05 09:33:22 - INFO - 操作模式检测完成，模式: route，主机名: KHU-FGT-1
2025-08-05 09:33:22 - INFO - 管道阶段：阶段完成
2025-08-05 09:33:22 - INFO - 阶段开始: fortigate_conversion -> interface_processing (2/12)
2025-08-05 09:33:22 - INFO - 管道阶段：阶段开始
2025-08-05 09:33:22 - INFO - 开始处理接口配置
2025-08-05 09:33:22 - INFO - 开始接口处理，共58个项目
2025-08-05 09:33:22 - WARNING - 接口 'ha' 警告：缺少接口映射
2025-08-05 09:33:22 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:22 - WARNING - 接口 'port1' 警告：缺少接口映射
2025-08-05 09:33:22 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'port4' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'port7' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'port8' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'port10' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'port11' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'port12' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 's1' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 's2' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'vw1' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'vw2' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - interface_processor.unsupported_access_service
2025-08-05 09:33:23 - INFO - 过滤modem接口: modem
2025-08-05 09:33:23 - WARNING - 接口 'naf.root' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'l2t.root' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'ssl.root' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'Vodafone_MPLS' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - interface_processor.unsupported_access_service
2025-08-05 09:33:23 - WARNING - interface_processor.unsupported_access_service
2025-08-05 09:33:23 - WARNING - interface_processor.unsupported_access_service
2025-08-05 09:33:23 - WARNING - interface_processor.unsupported_access_service
2025-08-05 09:33:23 - WARNING - interface_processor.unsupported_access_service
2025-08-05 09:33:23 - WARNING - interface_processor.unsupported_access_service
2025-08-05 09:33:23 - WARNING - interface_processor.unsupported_access_service
2025-08-05 09:33:23 - WARNING - interface_processor.unsupported_access_service
2025-08-05 09:33:23 - WARNING - 接口 'UITSEC_Forti' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'UITSEC_CP' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'BBS_PAM' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 接口 'BBS_PAM1' 警告：缺少接口映射
2025-08-05 09:33:23 - INFO - 影响：请在映射文件中添加该接口的映射关系
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: 'mgmt': 'Ge0/0', 'port2': 'Ge0/2', 'port3': 'Ge0/3', 'port5': 'Ge0/5', 'port6': 'Ge0/6', 'port9': 'Ge0/1', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'Vlan55': 'TenGe0/1.55', 'Vlan99': 'TenGe0/1.99', 'Vlan100': 'TenGe0/1.100', 'Vlan103': 'TenGe0/1.103', 'Vlan104': 'TenGe0/1.104', 'Vlan105': 'TenGe0/1.105', 'Vlan106': 'TenGe0/1.106', 'Vlan107': 'TenGe0/1.107', 'Vlan108': 'TenGe0/1.108', 'Vlan109': 'TenGe0/1.109', 'Vlan110': 'TenGe0/1.110', 'Vlan111': 'TenGe0/1.111', 'Vlan113': 'TenGe0/1.113', 'Vlan115': 'TenGe0/1.115', 'Vlan116': 'TenGe0/1.116', 'Vlan117': 'TenGe0/1.117', 'Vlan120': 'TenGe0/1.120', 'Vlan136': 'TenGe0/1.136', 'Vlan150': 'TenGe0/1.150', 'Vlan151': 'TenGe0/1.151', 'Vlan300': 'TenGe0/1.300', 'Vlan130': 'TenGe0/1.130', 'Vlan155': 'TenGe0/1.155', 'Vlan200': 'TenGe0/1.200', 'Vlan222': 'TenGe0/1.222', 'VLAN101': 'TenGe0/1.101', 'TO-CHECKPOINT': 'TenGe0/1.145', 'Vlan114': 'TenGe0/1.114', 'RUIJIE-MGMT': 'TenGe0/1.199'
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: 'mgmt'
2025-08-05 09:33:23 - INFO - DEBUG: 开始生成XML片段，converted接口数量: 37
2025-08-05 09:33:23 - INFO - DEBUG: physical_interfaces数量: 8
2025-08-05 09:33:23 - INFO - DEBUG: vlan_interfaces数量: 29
2025-08-05 09:33:23 - INFO - DEBUG: XML片段生成 - converted_interfaces: 37
2025-08-05 09:33:23 - INFO - DEBUG: XML片段生成 - vlan_interfaces: 29
2025-08-05 09:33:23 - INFO - DEBUG: XML片段生成 - physical_interfaces: 8
2025-08-05 09:33:23 - INFO - PhysicalInterfaceHandler初始化 - 非透明模式，transparent_mode_result保持None
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.55
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.55 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.55 配置为静态IP模式: **********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.55
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.99
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.99 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.99 配置为静态IP模式: *********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.99
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.100
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.100 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.100 配置为静态IP模式: **********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.100
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.103
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.103 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.103 配置为静态IP模式: ********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.103
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.104
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.104 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.104 配置为静态IP模式: ********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.104
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.105
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.105 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.105 配置为静态IP模式: ********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.105
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.106
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.106 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.106 配置为静态IP模式: ********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.106
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.107
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.107 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.107 配置为静态IP模式: ********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.107
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.108
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.108 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.108 配置为静态IP模式: ********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.108
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.109
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.109 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.109 配置为静态IP模式: ********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.109
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.110
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.110 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.110 配置为静态IP模式: *********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.110
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.111
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.111 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.111 配置为静态IP模式: *********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.111
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.113
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.113 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.113 配置为静态IP模式: *********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.113
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.115
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.115 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.115 配置为静态IP模式: *********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.115
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.116
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.116 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.116 配置为静态IP模式: *********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.116
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.117
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.117 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.117 配置为静态IP模式: *********/19
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.117
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.120
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.120 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.120 配置为静态IP模式: *********/23
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.120
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.136
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.136 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.136 配置为静态IP模式: *********/22
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.136
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.150
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.150 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.150 配置为静态IP模式: *********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.150
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.151
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.151 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.151 配置为静态IP模式: *********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.151
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.300
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.300 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.300 配置为静态IP模式: *********/22
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.300
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.130
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.130 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.130 配置为静态IP模式: **********/16
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.130
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.155
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.155 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.155 配置为静态IP模式: **********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.155
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.200
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.200 添加IPv4配置，模式: 
2025-08-05 09:33:23 - WARNING - 子接口 TenGe0/1.200 未找到明确的IP配置，默认使用DHCP模式
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.200
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.222
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.222 添加IPv4配置，模式: 
2025-08-05 09:33:23 - WARNING - 子接口 TenGe0/1.222 未找到明确的IP配置，默认使用DHCP模式
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.222
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.101
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.101 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.101 配置为静态IP模式: ********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.101
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.145
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.145 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.145 配置为静态IP模式: *********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.145
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.114
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.114 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.114 配置为静态IP模式: *********/24
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.114
2025-08-05 09:33:23 - INFO - 创建新VLAN接口: TenGe0/1.199
2025-08-05 09:33:23 - INFO - interface handler: mapped parent interface
2025-08-05 09:33:23 - INFO - 为子接口 TenGe0/1.199 添加IPv4配置，模式: 
2025-08-05 09:33:23 - INFO - 子接口 TenGe0/1.199 配置为静态IP模式: *************/23
2025-08-05 09:33:23 - INFO - VLAN接口已创建: TenGe0/1.199
2025-08-05 09:33:23 - INFO - DEBUG: 开始处理物理接口，数量: 8
2025-08-05 09:33:23 - INFO - DEBUG: 处理物理接口 mgmt, is_subinterface: False
2025-08-05 09:33:23 - INFO - DEBUG: 为接口 mgmt 生成XML
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:33:23 - INFO - interface handler: create new interface
2025-08-05 09:33:23 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:33:23 - INFO - interface handler: create fortinet interface
2025-08-05 09:33:23 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:33:23 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:33:23 - INFO - interface handler: configured static ip
2025-08-05 09:33:23 - INFO - DEBUG: 最终的combined_access: ['ping', 'https']
2025-08-05 09:33:23 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: false
2025-08-05 09:33:23 - INFO - interface handler: configured access control
2025-08-05 09:33:23 - INFO - interface handler: fortinet interface created
2025-08-05 09:33:23 - INFO - DEBUG: 接口 mgmt XML生成完成
2025-08-05 09:33:23 - INFO - DEBUG: 处理物理接口 port2, is_subinterface: False
2025-08-05 09:33:23 - INFO - DEBUG: 为接口 port2 生成XML
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:33:23 - INFO - interface handler: create new interface
2025-08-05 09:33:23 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:33:23 - INFO - interface handler: create fortinet interface
2025-08-05 09:33:23 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:33:23 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:33:23 - INFO - interface handler: configured static ip
2025-08-05 09:33:23 - INFO - DEBUG: 最终的combined_access: ['ping', 'https']
2025-08-05 09:33:23 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: false
2025-08-05 09:33:23 - INFO - interface handler: configured access control
2025-08-05 09:33:23 - INFO - interface handler: fortinet interface created
2025-08-05 09:33:23 - INFO - DEBUG: 接口 port2 XML生成完成
2025-08-05 09:33:23 - INFO - DEBUG: 处理物理接口 port3, is_subinterface: False
2025-08-05 09:33:23 - INFO - DEBUG: 为接口 port3 生成XML
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:33:23 - INFO - interface handler: create new interface
2025-08-05 09:33:23 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:33:23 - INFO - interface handler: create fortinet interface
2025-08-05 09:33:23 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:33:23 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:33:23 - INFO - interface handler: configured static ip
2025-08-05 09:33:23 - INFO - DEBUG: 最终的combined_access: ['ping']
2025-08-05 09:33:23 - INFO - DEBUG: 最终XML值 - https: false, ping: true, ssh: false
2025-08-05 09:33:23 - INFO - interface handler: configured access control
2025-08-05 09:33:23 - INFO - interface handler: fortinet interface created
2025-08-05 09:33:23 - INFO - DEBUG: 接口 port3 XML生成完成
2025-08-05 09:33:23 - INFO - DEBUG: 处理物理接口 port5, is_subinterface: False
2025-08-05 09:33:23 - INFO - DEBUG: 为接口 port5 生成XML
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:33:23 - INFO - interface handler: create new interface
2025-08-05 09:33:23 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:33:23 - INFO - interface handler: create fortinet interface
2025-08-05 09:33:23 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:33:23 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:33:23 - INFO - interface handler: configured static ip
2025-08-05 09:33:23 - INFO - DEBUG: 最终的combined_access: ['ping', 'https']
2025-08-05 09:33:23 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: false
2025-08-05 09:33:23 - INFO - interface handler: configured access control
2025-08-05 09:33:23 - INFO - interface handler: fortinet interface created
2025-08-05 09:33:23 - INFO - DEBUG: 接口 port5 XML生成完成
2025-08-05 09:33:23 - INFO - DEBUG: 处理物理接口 port6, is_subinterface: False
2025-08-05 09:33:23 - INFO - DEBUG: 为接口 port6 生成XML
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:33:23 - INFO - interface handler: create new interface
2025-08-05 09:33:23 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:33:23 - INFO - interface handler: create fortinet interface
2025-08-05 09:33:23 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:33:23 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:33:23 - INFO - interface handler: configured static ip
2025-08-05 09:33:23 - INFO - DEBUG: 最终的combined_access: ['ping', 'https']
2025-08-05 09:33:23 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: false
2025-08-05 09:33:23 - INFO - interface handler: configured access control
2025-08-05 09:33:23 - INFO - interface handler: fortinet interface created
2025-08-05 09:33:23 - INFO - DEBUG: 接口 port6 XML生成完成
2025-08-05 09:33:23 - INFO - DEBUG: 处理物理接口 port9, is_subinterface: False
2025-08-05 09:33:23 - INFO - DEBUG: 为接口 port9 生成XML
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:33:23 - INFO - interface handler: create new interface
2025-08-05 09:33:23 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:33:23 - INFO - interface handler: create fortinet interface
2025-08-05 09:33:23 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:33:23 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:33:23 - INFO - interface handler: configured static ip
2025-08-05 09:33:23 - INFO - DEBUG: 最终的combined_access: ['ping']
2025-08-05 09:33:23 - INFO - DEBUG: 最终XML值 - https: false, ping: true, ssh: false
2025-08-05 09:33:23 - INFO - interface handler: configured access control
2025-08-05 09:33:23 - INFO - interface handler: fortinet interface created
2025-08-05 09:33:23 - INFO - DEBUG: 接口 port9 XML生成完成
2025-08-05 09:33:23 - INFO - DEBUG: 处理物理接口 x1, is_subinterface: False
2025-08-05 09:33:23 - INFO - DEBUG: 为接口 x1 生成XML
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:33:23 - INFO - interface handler: create new interface
2025-08-05 09:33:23 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:33:23 - INFO - interface handler: create fortinet interface
2025-08-05 09:33:23 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:33:23 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:33:23 - INFO - interface handler: configured static ip
2025-08-05 09:33:23 - INFO - DEBUG: 最终的combined_access: ['ping']
2025-08-05 09:33:23 - INFO - DEBUG: 最终XML值 - https: false, ping: true, ssh: false
2025-08-05 09:33:23 - INFO - interface handler: configured access control
2025-08-05 09:33:23 - INFO - interface handler: fortinet interface created
2025-08-05 09:33:23 - INFO - DEBUG: 接口 x1 XML生成完成
2025-08-05 09:33:23 - INFO - DEBUG: 处理物理接口 x2, is_subinterface: False
2025-08-05 09:33:23 - INFO - DEBUG: 为接口 x2 生成XML
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: interface_name
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data.keys())
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: list(intf_data
2025-08-05 09:33:23 - INFO - interface handler: create new interface
2025-08-05 09:33:23 - INFO - interface handler: using fortinet optimizer
2025-08-05 09:33:23 - INFO - interface handler: create fortinet interface
2025-08-05 09:33:23 - INFO - [待翻译] interface_handler.working_mode_route_nat
2025-08-05 09:33:23 - INFO - 🔍 DEBUG: - transparent_mode_result: None
2025-08-05 09:33:23 - INFO - interface handler: configured static ip
2025-08-05 09:33:23 - INFO - DEBUG: 最终的combined_access: ['ping', 'ssh', 'https']
2025-08-05 09:33:23 - INFO - DEBUG: 最终XML值 - https: true, ping: true, ssh: true
2025-08-05 09:33:23 - INFO - interface handler: configured access control
2025-08-05 09:33:23 - INFO - interface handler: fortinet interface created
2025-08-05 09:33:23 - INFO - DEBUG: 接口 x2 XML生成完成
2025-08-05 09:33:23 - INFO - DEBUG: XML片段生成完成，长度: 30100
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: 'mgmt': 'Ge0/0', 'port2': 'Ge0/2', 'port3': 'Ge0/3', 'port5': 'Ge0/5', 'port6': 'Ge0/6', 'port9': 'Ge0/1', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'Vlan55': 'TenGe0/1.55', 'Vlan99': 'TenGe0/1.99', 'Vlan100': 'TenGe0/1.100', 'Vlan103': 'TenGe0/1.103', 'Vlan104': 'TenGe0/1.104', 'Vlan105': 'TenGe0/1.105', 'Vlan106': 'TenGe0/1.106', 'Vlan107': 'TenGe0/1.107', 'Vlan108': 'TenGe0/1.108', 'Vlan109': 'TenGe0/1.109', 'Vlan110': 'TenGe0/1.110', 'Vlan111': 'TenGe0/1.111', 'Vlan113': 'TenGe0/1.113', 'Vlan115': 'TenGe0/1.115', 'Vlan116': 'TenGe0/1.116', 'Vlan117': 'TenGe0/1.117', 'Vlan120': 'TenGe0/1.120', 'Vlan136': 'TenGe0/1.136', 'Vlan150': 'TenGe0/1.150', 'Vlan151': 'TenGe0/1.151', 'Vlan300': 'TenGe0/1.300', 'Vlan130': 'TenGe0/1.130', 'Vlan155': 'TenGe0/1.155', 'Vlan200': 'TenGe0/1.200', 'Vlan222': 'TenGe0/1.222', 'VLAN101': 'TenGe0/1.101', 'TO-CHECKPOINT': 'TenGe0/1.145', 'Vlan114': 'TenGe0/1.114', 'RUIJIE-MGMT': 'TenGe0/1.199'
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: 'mgmt'
2025-08-05 09:33:23 - INFO - DEBUG: 接口映射 1: mgmt -> Ge0/0 (类型: physical)
2025-08-05 09:33:23 - INFO - DEBUG: 接口映射 2: port2 -> Ge0/2 (类型: physical)
2025-08-05 09:33:23 - INFO - DEBUG: 接口映射 3: port3 -> Ge0/3 (类型: physical)
2025-08-05 09:33:23 - INFO - DEBUG: 接口映射 4: port5 -> Ge0/5 (类型: physical)
2025-08-05 09:33:23 - INFO - DEBUG: 接口映射 5: port6 -> Ge0/6 (类型: physical)
2025-08-05 09:33:23 - INFO - DEBUG: 接口映射 6: port9 -> Ge0/1 (类型: physical)
2025-08-05 09:33:23 - INFO - DEBUG: 接口映射 7: x1 -> TenGe0/0 (类型: physical)
2025-08-05 09:33:23 - INFO - DEBUG: 接口映射 8: x2 -> TenGe0/1 (类型: physical)
2025-08-05 09:33:23 - INFO - DEBUG: 接口映射 9: Vlan55 -> TenGe0/1.55 (类型: vlan)
2025-08-05 09:33:23 - INFO - DEBUG: 接口映射 10: Vlan99 -> TenGe0/1.99 (类型: vlan)
2025-08-05 09:33:23 - INFO - 最终接口映射已创建
2025-08-05 09:33:23 - INFO - 接口映射已保存
2025-08-05 09:33:23 - INFO - 最终接口映射已保存
2025-08-05 09:33:23 - INFO - [待翻译] interface_processing_stage.final_mapping_saved
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: 'mgmt': 'Ge0/0', 'port2': 'Ge0/2', 'port3': 'Ge0/3', 'port5': 'Ge0/5', 'port6': 'Ge0/6', 'port9': 'Ge0/1', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'Vlan55': 'TenGe0/1.55', 'Vlan99': 'TenGe0/1.99', 'Vlan100': 'TenGe0/1.100', 'Vlan103': 'TenGe0/1.103', 'Vlan104': 'TenGe0/1.104', 'Vlan105': 'TenGe0/1.105', 'Vlan106': 'TenGe0/1.106', 'Vlan107': 'TenGe0/1.107', 'Vlan108': 'TenGe0/1.108', 'Vlan109': 'TenGe0/1.109', 'Vlan110': 'TenGe0/1.110', 'Vlan111': 'TenGe0/1.111', 'Vlan113': 'TenGe0/1.113', 'Vlan115': 'TenGe0/1.115', 'Vlan116': 'TenGe0/1.116', 'Vlan117': 'TenGe0/1.117', 'Vlan120': 'TenGe0/1.120', 'Vlan136': 'TenGe0/1.136', 'Vlan150': 'TenGe0/1.150', 'Vlan151': 'TenGe0/1.151', 'Vlan300': 'TenGe0/1.300', 'Vlan130': 'TenGe0/1.130', 'Vlan155': 'TenGe0/1.155', 'Vlan200': 'TenGe0/1.200', 'Vlan222': 'TenGe0/1.222', 'VLAN101': 'TenGe0/1.101', 'TO-CHECKPOINT': 'TenGe0/1.145', 'Vlan114': 'TenGe0/1.114', 'RUIJIE-MGMT': 'TenGe0/1.199'
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: 'mgmt'
2025-08-05 09:33:23 - INFO - DEBUG: 保存的映射内容: {'mgmt': 'Ge0/0', 'port2': 'Ge0/2', 'port3': 'Ge0/3', 'port5': 'Ge0/5', 'port6': 'Ge0/6', 'port9': 'Ge0/1', 'x1': 'TenGe0/0', 'x2': 'TenGe0/1', 'Vlan55': 'TenGe0/1.55', 'Vlan99': 'TenGe0/1.99', 'Vlan100': 'TenGe0/1.100', 'Vlan103': 'TenGe0/1.103', 'Vlan104': 'TenGe0/1.104', 'Vlan105': 'TenGe0/1.105', 'Vlan106': 'TenGe0/1.106', 'Vlan107': 'TenGe0/1.107', 'Vlan108': 'TenGe0/1.108', 'Vlan109': 'TenGe0/1.109', 'Vlan110': 'TenGe0/1.110', 'Vlan111': 'TenGe0/1.111', 'Vlan113': 'TenGe0/1.113', 'Vlan115': 'TenGe0/1.115', 'Vlan116': 'TenGe0/1.116', 'Vlan117': 'TenGe0/1.117', 'Vlan120': 'TenGe0/1.120', 'Vlan136': 'TenGe0/1.136', 'Vlan150': 'TenGe0/1.150', 'Vlan151': 'TenGe0/1.151', 'Vlan300': 'TenGe0/1.300', 'Vlan130': 'TenGe0/1.130', 'Vlan155': 'TenGe0/1.155', 'Vlan200': 'TenGe0/1.200', 'Vlan222': 'TenGe0/1.222', 'VLAN101': 'TenGe0/1.101', 'TO-CHECKPOINT': 'TenGe0/1.145', 'Vlan114': 'TenGe0/1.114', 'RUIJIE-MGMT': 'TenGe0/1.199'}
2025-08-05 09:33:23 - INFO - 接口处理完成：成功转换37/58，耗时674ms
2025-08-05 09:33:23 - INFO - 接口处理：21个项目被跳过
2025-08-05 09:33:23 - INFO - 接口处理完成，转换: 37，安全区域: 1
2025-08-05 09:33:23 - INFO - 管道阶段：阶段完成
2025-08-05 09:33:23 - INFO - 阶段开始: fortigate_conversion -> service_processing (3/12)
2025-08-05 09:33:23 - INFO - 管道阶段：阶段开始
2025-08-05 09:33:23 - INFO - 开始服务对象处理
2025-08-05 09:33:23 - INFO - 没有找到服务对象配置
2025-08-05 09:33:23 - INFO - 开始服务对象处理
2025-08-05 09:33:23 - INFO - 服务对象处理完成，耗时1ms
2025-08-05 09:33:23 - INFO - 管道阶段：阶段完成
2025-08-05 09:33:23 - INFO - 阶段开始: fortigate_conversion -> address_processing (4/12)
2025-08-05 09:33:23 - INFO - 管道阶段：阶段开始
2025-08-05 09:33:23 - INFO - 开始处理地址对象
2025-08-05 09:33:23 - INFO - 发现 0 个动态生成的地址对象
2025-08-05 09:33:23 - INFO - 地址对象统计: 原始=0, VIP=0, 动态生成=0, 总计=0
2025-08-05 09:33:23 - INFO - 没有地址对象需要处理
2025-08-05 09:33:23 - INFO - 开始地址对象处理
2025-08-05 09:33:23 - INFO - 地址对象处理完成，耗时2ms
2025-08-05 09:33:23 - INFO - 管道阶段：阶段完成
2025-08-05 09:33:23 - INFO - 阶段开始: fortigate_conversion -> address_group_processing (5/12)
2025-08-05 09:33:23 - INFO - 管道阶段：阶段开始
2025-08-05 09:33:23 - INFO - 开始处理地址对象组
2025-08-05 09:33:23 - INFO - 没有地址对象组需要处理
2025-08-05 09:33:23 - INFO - 管道阶段：阶段完成
2025-08-05 09:33:23 - INFO - 阶段开始: fortigate_conversion -> service_group_processing (6/12)
2025-08-05 09:33:23 - INFO - 管道阶段：阶段开始
2025-08-05 09:33:23 - INFO - 开始处理服务对象组
2025-08-05 09:33:23 - INFO - 没有服务对象组需要处理
2025-08-05 09:33:23 - INFO - 管道阶段：阶段完成
2025-08-05 09:33:23 - INFO - 阶段开始: fortigate_conversion -> zone_processing (7/12)
2025-08-05 09:33:23 - INFO - 管道阶段：阶段开始
2025-08-05 09:33:23 - INFO - 开始处理安全区域
2025-08-05 09:33:23 - INFO - Using interface mapping from context: 8 mappings
2025-08-05 09:33:23 - INFO - Interface mapping set directly: 8 mappings
2025-08-05 09:33:23 - INFO - 找到 0 个区域配置，操作模式: route
2025-08-05 09:33:23 - INFO - Interface mapping loaded
2025-08-05 09:33:23 - INFO - Interface mapping loaded (flat format): 37 mappings
2025-08-05 09:33:23 - WARNING - 消息中缺少参数: 'untrust', 'trust'
2025-08-05 09:33:23 - WARNING - Zone skipped: untrust - 区域的所有接口都未配置映射关系或区域没有配置接口
2025-08-05 09:33:23 - WARNING - Zone skipped: trust - 区域的所有接口都未配置映射关系或区域没有配置接口
2025-08-05 09:33:23 - INFO - 区域处理完成: 转换成功 0/0
2025-08-05 09:33:23 - INFO - 管道阶段：阶段完成
2025-08-05 09:33:23 - INFO - 阶段开始: fortigate_conversion -> time_range_processing (8/12)
2025-08-05 09:33:23 - INFO - 管道阶段：阶段开始
2025-08-05 09:33:23 - INFO - 开始处理时间对象
2025-08-05 09:33:23 - INFO - 找到 0 个时间对象
2025-08-05 09:33:23 - INFO - 没有时间对象需要处理
2025-08-05 09:33:23 - INFO - 管道阶段：阶段完成
2025-08-05 09:33:23 - INFO - 阶段开始: fortigate_conversion -> dns_processing (9/12)
2025-08-05 09:33:23 - INFO - 管道阶段：阶段开始
2025-08-05 09:33:23 - INFO - 开始DNS处理
2025-08-05 09:33:23 - INFO - 没有配置DNS服务器
2025-08-05 09:33:23 - INFO - DNS配置处理完成: DNS服务器 0 个，静态域名 0 个
2025-08-05 09:33:23 - INFO - DNS处理完成: DNS服务器 0 个，静态域名 0 个
2025-08-05 09:33:23 - INFO - 管道阶段：阶段完成
2025-08-05 09:33:23 - INFO - 阶段开始: fortigate_conversion -> static_route_processing (10/12)
2025-08-05 09:33:23 - INFO - 管道阶段：阶段开始
2025-08-05 09:33:23 - INFO - 开始静态路由处理
2025-08-05 09:33:23 - INFO - 检测到列表格式的静态路由数据，转换为字典格式
2025-08-05 09:33:23 - INFO - 转换后找到 0 个静态路由
2025-08-05 09:33:23 - INFO - 没有静态路由需要处理
2025-08-05 09:33:23 - INFO - 管道阶段：阶段完成
2025-08-05 09:33:23 - INFO - 阶段开始: fortigate_conversion -> fortigate_policy_conversion (11/12)
2025-08-05 09:33:23 - INFO - 管道阶段：阶段开始
2025-08-05 09:33:23 - ERROR - 数据上下文添加错误: fortigate_policy_stage.interface_mapping_validation_failed (阶段: None)
2025-08-05 09:33:23 - ERROR - 数据上下文添加错误: pipeline_stage.input_validation_failed (阶段: fortigate_policy_conversion)
2025-08-05 09:33:23 - ERROR - Pipeline stopped due to error: fortigate_conversion -> fortigate_policy_conversion
2025-08-05 09:33:23 - INFO - Pipeline execution completed: fortigate_conversion, state: failed, duration: 0.77s, errors: 2, warnings: 0
2025-08-05 09:33:23 - INFO - 🔍 DEBUG: 新架构管道执行完成，状态: failed
2025-08-05 09:33:23 - INFO - 转换已完成
2025-08-05 09:33:23 - ERROR - 转换过程中发生错误
